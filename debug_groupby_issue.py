#!/usr/bin/env python3
"""
调试groupby数据类型问题
"""

import sys
import os
import pandas as pd
from datetime import datetime
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.basic.db_pro2_sea_air_profit import (
    query_job_details_by_date,
    query_business_details_by_date
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_groupby_issue():
    """
    调试groupby数据类型问题
    """
    print("=== 调试groupby数据类型问题 ===")
    
    # 设置日期范围
    begin_date = "2025-05-01"
    end_date = "2025-05-31"
    
    print(f"查询日期范围: {begin_date} to {end_date}")
    
    # 1. 查询Job数据
    job_details = query_job_details_by_date(
        begin_date=begin_date,
        end_date=end_date,
        include_profit=True,
        logger_prefix="[DEBUG_JOB]"
    )
    
    # 2. 查询Booking数据
    os.environ['TRANSHIPMENT_NEED'] = 'False'
    
    booking_details = query_business_details_by_date(
        begin_date=begin_date,
        end_date=end_date,
        business_type='all',
        include_business_no=True,
        include_profit=True,
        logger_prefix="[DEBUG_BOOKING]"
    )
    
    if not job_details or not booking_details:
        print("❌ 没有数据")
        return False
    
    # 转换为DataFrame
    job_df = pd.DataFrame(job_details)
    booking_df = pd.DataFrame(booking_details)
    
    print(f"Job数据: {len(job_df)} 条")
    print(f"Booking数据: {len(booking_df)} 条")
    
    # 获取job_file_ids
    job_file_ids = job_df['job_file_id'].tolist()
    print(f"Job file IDs数量: {len(job_file_ids)}")
    print(f"Job file IDs类型: {type(job_file_ids[0])}")
    print(f"Job file IDs示例: {job_file_ids[:5]}")
    
    # 筛选相关booking数据
    job_file_ids_set = set(job_file_ids)
    relevant_bookings = booking_df[booking_df['job_file_id'].isin(job_file_ids_set)]
    
    print(f"相关Booking数据: {len(relevant_bookings)} 条")
    print(f"Booking job_file_id类型: {type(relevant_bookings['job_file_id'].iloc[0])}")
    print(f"Booking job_file_id示例: {relevant_bookings['job_file_id'].head().tolist()}")
    
    # 测试groupby
    print("\n--- 测试groupby ---")
    if len(relevant_bookings) > 0:
        booking_groups = relevant_bookings.groupby('job_file_id')
        print(f"Groupby groups数量: {len(booking_groups)}")
        print(f"Groupby keys类型: {type(list(booking_groups.groups.keys())[0])}")
        print(f"Groupby keys示例: {list(booking_groups.groups.keys())[:5]}")
        
        # 测试访问
        test_job_id = job_file_ids[0]
        print(f"\n测试访问job_file_id: {test_job_id} (类型: {type(test_job_id)})")
        
        # 方法1：直接检查
        if test_job_id in booking_groups.groups:
            print("✅ 方法1：直接in检查 - 成功")
        else:
            print("❌ 方法1：直接in检查 - 失败")
        
        # 方法2：get_group
        try:
            test_group = booking_groups.get_group(test_job_id)
            print(f"✅ 方法2：get_group - 成功，获得{len(test_group)}条记录")
        except KeyError as e:
            print(f"❌ 方法2：get_group - 失败: {e}")
        
        # 方法3：类型转换后检查
        booking_groups_keys = set(booking_groups.groups.keys())
        if test_job_id in booking_groups_keys:
            print("✅ 方法3：转换为set后检查 - 成功")
        else:
            print("❌ 方法3：转换为set后检查 - 失败")
            
        # 检查数据类型差异
        print(f"\nJob ID类型: {type(test_job_id)}")
        print(f"Booking group keys类型: {[type(k) for k in list(booking_groups.groups.keys())[:3]]}")
        
        # 尝试类型转换
        if hasattr(test_job_id, 'item'):  # numpy类型
            test_job_id_converted = test_job_id.item()
            print(f"转换后Job ID类型: {type(test_job_id_converted)}")
            
            if test_job_id_converted in booking_groups.groups:
                print("✅ 类型转换后检查 - 成功")
            else:
                print("❌ 类型转换后检查 - 失败")
    
    # 模拟统计函数的逻辑
    print("\n--- 模拟统计函数逻辑 ---")
    
    # 统计有booking数据的job数量
    jobs_with_bookings = 0
    jobs_without_bookings = 0
    
    for job_file_id in job_file_ids:
        if job_file_id in booking_groups.groups:
            jobs_with_bookings += 1
        else:
            jobs_without_bookings += 1
            print(f"没有booking数据的Job ID: {job_file_id} (类型: {type(job_file_id)})")
    
    print(f"有booking数据的Job数: {jobs_with_bookings}")
    print(f"没有booking数据的Job数: {jobs_without_bookings}")
    
    # 如果有问题，尝试修复
    if jobs_without_bookings > 0:
        print("\n--- 尝试修复数据类型问题 ---")
        
        # 确保job_file_id的数据类型一致
        job_df['job_file_id'] = job_df['job_file_id'].astype(int)
        booking_df['job_file_id'] = booking_df['job_file_id'].astype(int)
        
        # 重新筛选和分组
        job_file_ids_fixed = job_df['job_file_id'].tolist()
        relevant_bookings_fixed = booking_df[booking_df['job_file_id'].isin(job_file_ids_fixed)]
        booking_groups_fixed = relevant_bookings_fixed.groupby('job_file_id')
        
        # 重新统计
        jobs_with_bookings_fixed = 0
        jobs_without_bookings_fixed = 0
        
        for job_file_id in job_file_ids_fixed:
            if job_file_id in booking_groups_fixed.groups:
                jobs_with_bookings_fixed += 1
            else:
                jobs_without_bookings_fixed += 1
        
        print(f"修复后 - 有booking数据的Job数: {jobs_with_bookings_fixed}")
        print(f"修复后 - 没有booking数据的Job数: {jobs_without_bookings_fixed}")
        
        if jobs_without_bookings_fixed == 0:
            print("✅ 数据类型问题已修复")
        else:
            print("❌ 数据类型问题未解决")
    
    return True

def main():
    """
    主函数
    """
    try:
        print("开始调试groupby数据类型问题")
        
        success = debug_groupby_issue()
        
        if success:
            print("\n✅ 调试完成")
        else:
            print("\n❌ 调试失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        logger.error(f"Program failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
