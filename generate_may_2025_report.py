#!/usr/bin/env python3
"""
生成2025年5月的完整Excel报告
"""

import sys
import os
import pandas as pd
from datetime import datetime
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.basic.db_pro2_sea_air_profit import query_job_details_with_statistics_by_date
from utils.basic.excel_export import export_job_details_to_excel

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def generate_may_2025_report():
    """
    生成2025年5月的完整Excel报告
    """
    print("=== 生成2025年5月Excel报告 ===")
    
    # 设置日期范围
    begin_date = "2025-05-01"
    end_date = "2025-05-31"
    
    print(f"查询日期范围: {begin_date} to {end_date}")
    
    # 设置环境变量
    os.environ['TRANSHIPMENT_NEED'] = 'True'  # 包括转运利润
    
    try:
        # 查询Job详情和统计数据
        print("正在查询Job详情和统计数据...")
        job_details = query_job_details_with_statistics_by_date(
            begin_date=begin_date,
            end_date=end_date,
            include_profit=True,
            logger_prefix="[MAY_2025_REPORT]"
        )
        
        if not job_details:
            print("❌ 没有找到Job数据")
            return False
        
        print(f"✅ 查询到 {len(job_details)} 条Job记录")
        
        # 转换为DataFrame进行分析
        df = pd.DataFrame(job_details)
        
        # 统计分析
        total_bk_count = df['bk_count'].sum()
        total_bl_count = df['bl_count'].sum()
        jobs_with_booking = len(df[df['bk_count'] > 0])
        jobs_with_bl = len(df[df['bl_count'] > 0])
        total_profit = df['profit'].sum()
        total_transhipment_profit = df['transhipment_profit'].sum()
        
        print(f"\n=== 统计摘要 ===")
        print(f"总Job数: {len(job_details)}")
        print(f"有booking数据的Job数: {jobs_with_booking}")
        print(f"有BL数据的Job数: {jobs_with_bl}")
        print(f"总booking数: {total_bk_count}")
        print(f"总BL数: {total_bl_count}")
        print(f"总利润: {total_profit:,.2f}")
        print(f"转运利润: {total_transhipment_profit:,.2f}")
        
        # 生成Excel文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"job_details_2025_05_{timestamp}.xlsx"
        
        print(f"\n正在生成Excel文件: {filename}")
        
        success = export_job_details_to_excel(
            job_details=job_details,
            filename=filename,
            include_statistics=True
        )
        
        if success:
            print(f"✅ Excel文件生成成功: {filename}")
            
            # 显示文件路径
            current_dir = os.getcwd()
            full_path = os.path.join(current_dir, filename)
            print(f"文件路径: {full_path}")
            
            return True
        else:
            print("❌ Excel文件生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 生成报告失败: {e}")
        logger.error(f"Report generation failed: {e}", exc_info=True)
        return False

def main():
    """
    主函数
    """
    try:
        print("开始生成2025年5月Excel报告")
        
        success = generate_may_2025_report()
        
        if success:
            print("\n✅ 报告生成完成")
        else:
            print("\n❌ 报告生成失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        logger.error(f"Program failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
