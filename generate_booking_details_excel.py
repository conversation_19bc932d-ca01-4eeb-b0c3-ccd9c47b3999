#!/usr/bin/env python3
"""
生成Booking级别的业务明细Excel报告（包含转运利润）
"""

import sys
import os
import pandas as pd
from datetime import datetime
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.basic.db_pro2_sea_air_profit import get_booking_details_with_transhipment

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def generate_booking_details_excel():
    """
    生成Booking级别的业务明细Excel报告
    """
    print("=== 生成Booking级别业务明细Excel报告 ===")
    
    # 设置日期范围（可以根据需要修改）
    begin_date = "2025-05-01"
    end_date = "2025-05-31"
    
    print(f"查询日期范围: {begin_date} to {end_date}")
    
    # 设置环境变量以包含转运利润
    os.environ['TRANSHIPMENT_NEED'] = 'True'
    print("设置 TRANSHIPMENT_NEED=True 以获取完整转运数据")
    
    try:
        # 查询Booking明细数据
        print("正在查询Booking明细数据...")
        start_time = datetime.now()
        
        booking_details = get_booking_details_with_transhipment(
            begin_date=begin_date,
            end_date=end_date,
            logger_prefix="[BOOKING_EXPORT]"
        )
        
        end_time = datetime.now()
        query_duration = (end_time - start_time).total_seconds()
        
        print(f"查询完成，耗时: {query_duration:.2f} 秒")
        print(f"获取到 {len(booking_details)} 条Booking记录")
        
        if not booking_details:
            print("❌ 没有找到任何Booking数据")
            return False
        
        # 转换为DataFrame进行分析
        df = pd.DataFrame(booking_details)
        
        # 统计分析
        print(f"\n=== 数据统计分析 ===")
        total_bookings = len(df)
        transhipment_bookings = len(df[df['is_transhipment'] == 1]) if 'is_transhipment' in df.columns else 0
        bookings_with_transhipment_profit = len(df[df['transhipment_profit'] > 0]) if 'transhipment_profit' in df.columns else 0
        
        total_income = df['income'].sum() if 'income' in df.columns else 0
        total_cost = df['cost'].sum() if 'cost' in df.columns else 0
        total_profit = df['profit'].sum() if 'profit' in df.columns else 0
        total_transhipment_profit = df['transhipment_profit'].sum() if 'transhipment_profit' in df.columns else 0
        
        print(f"总Booking数: {total_bookings}")
        print(f"转运Booking数: {transhipment_bookings}")
        print(f"有转运利润的Booking数: {bookings_with_transhipment_profit}")
        print(f"总收入: {total_income:,.2f}")
        print(f"总成本: {total_cost:,.2f}")
        print(f"总利润: {total_profit:,.2f}")
        print(f"总转运利润: {total_transhipment_profit:,.2f}")
        
        # 按业务类型统计
        if 'business_type' in df.columns:
            print(f"\n=== 按业务类型统计 ===")
            business_type_stats = df.groupby('business_type').agg({
                'jb_id': 'count',
                'income': 'sum',
                'cost': 'sum', 
                'profit': 'sum',
                'transhipment_profit': 'sum'
            }).round(2)
            business_type_stats.columns = ['Booking数', '收入', '成本', '利润', '转运利润']
            print(business_type_stats.to_string())
        
        # 生成Excel文件
        print("\n=== 生成Excel文件 ===")
        
        # 重新排列列的顺序，把重要字段放在前面
        column_order = [
            'jb_id', 'entity_id', 'business_type', 'business_type_name',
            'job_file_id', 'job_file_no', 'business_no',
            'income', 'cost', 'profit', 'transhipment_profit',
            'lcl_rt', 'teu', 'air_weight',
            'is_transhipment', 'transhipment_id'
        ]
        
        # 确保所有列都存在，如果不存在则添加默认值
        for col in column_order:
            if col not in df.columns:
                df[col] = 0 if col in ['income', 'cost', 'profit', 'transhipment_profit', 'lcl_rt', 'teu', 'air_weight'] else ''
        
        # 添加其他可能存在的列
        existing_columns = [col for col in column_order if col in df.columns]
        other_columns = [col for col in df.columns if col not in column_order]
        final_columns = existing_columns + other_columns
        
        # 重新排序DataFrame
        df_export = df[final_columns]
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"booking_details_2025_05_{timestamp}.xlsx"
        
        # 导出Excel
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 主数据表
            df_export.to_excel(writer, sheet_name='Booking明细', index=False)
            
            # 统计汇总表
            summary_data = {
                '统计项目': [
                    '总Booking数', '转运Booking数', '有转运利润的Booking数',
                    '总收入', '总成本', '总利润', '总转运利润'
                ],
                '数值': [
                    total_bookings,
                    transhipment_bookings,
                    bookings_with_transhipment_profit,
                    total_income,
                    total_cost,
                    total_profit,
                    total_transhipment_profit
                ]
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='统计汇总', index=False)
            
            # 业务类型汇总表
            if 'business_type' in df.columns:
                business_type_stats.to_excel(writer, sheet_name='业务类型汇总')
            
            # 转运数据详细分析
            if 'is_transhipment' in df.columns:
                transhipment_df = df[df['is_transhipment'] == 1]
                if len(transhipment_df) > 0:
                    transhipment_summary = transhipment_df.groupby('transhipment_id').agg({
                        'jb_id': 'count',
                        'profit': 'sum',
                        'transhipment_profit': 'sum'
                    }).round(2)
                    transhipment_summary.columns = ['关联Booking数', '本段利润', '转运利润']
                    transhipment_summary.to_excel(writer, sheet_name='转运数据汇总')
        
        print(f"✅ Excel文件已生成: {filename}")
        print(f"包含以下工作表:")
        print(f"  - Booking明细: {len(df_export)} 条记录")
        print(f"  - 统计汇总: 关键指标汇总")
        if 'business_type' in df.columns:
            print(f"  - 业务类型汇总: 按业务类型的统计数据")
        if transhipment_bookings > 0:
            print(f"  - 转运数据汇总: 转运业务详细分析")
        
        # 显示文件路径
        current_dir = os.getcwd()
        full_path = os.path.join(current_dir, filename)
        print(f"\n文件路径: {full_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成Excel失败: {e}")
        logger.error(f"Excel generation failed: {e}", exc_info=True)
        return False

def main():
    """
    主函数
    """
    try:
        print("开始生成Booking级别业务明细Excel报告")
        
        success = generate_booking_details_excel()
        
        if success:
            print("\n✅ Excel报告生成完成")
        else:
            print("\n❌ Excel报告生成失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        logger.error(f"Program failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
