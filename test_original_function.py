#!/usr/bin/env python3
"""
测试原始统计函数
"""

import sys
import os
import pandas as pd
from datetime import datetime
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.basic.db_pro2_sea_air_profit import query_job_details_with_statistics_by_date

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_original_function():
    """
    测试原始统计函数
    """
    print("=== 测试原始统计函数 ===")
    
    # 设置日期范围
    begin_date = "2025-05-01"
    end_date = "2025-05-31"
    
    print(f"查询日期范围: {begin_date} to {end_date}")
    
    # 设置环境变量
    os.environ['TRANSHIPMENT_NEED'] = 'False'
    
    # 调用原始函数
    try:
        result = query_job_details_with_statistics_by_date(
            begin_date=begin_date,
            end_date=end_date,
            logger_prefix="[TEST_ORIGINAL]"
        )
        
        print(f"✅ 函数调用成功")
        print(f"返回结果类型: {type(result)}")
        
        if isinstance(result, list) and len(result) > 0:
            print(f"结果数量: {len(result)}")
            
            # 转换为DataFrame进行分析
            df = pd.DataFrame(result)
            print(f"DataFrame字段: {list(df.columns)}")
            
            # 检查统计字段
            if 'bk_count' in df.columns and 'bl_count' in df.columns:
                bk_count_sum = df['bk_count'].sum()
                bl_count_sum = df['bl_count'].sum()
                bk_count_nonzero = (df['bk_count'] > 0).sum()
                bl_count_nonzero = (df['bl_count'] > 0).sum()
                
                print(f"\n=== 统计结果分析 ===")
                print(f"总bk_count: {bk_count_sum}")
                print(f"总bl_count: {bl_count_sum}")
                print(f"bk_count > 0的记录数: {bk_count_nonzero}")
                print(f"bl_count > 0的记录数: {bl_count_nonzero}")
                
                # 显示前几条记录
                print(f"\n前10条记录的统计字段:")
                stats_columns = ['job_file_id', 'business_type', 'bk_count', 'bl_count']
                if 'total_rt' in df.columns:
                    stats_columns.append('total_rt')
                if 'total_teu' in df.columns:
                    stats_columns.append('total_teu')
                
                print(df[stats_columns].head(10))
                
                # 检查是否所有统计都是0
                if bk_count_sum == 0 and bl_count_sum == 0:
                    print("❌ 所有Job的booking统计都是0 - 这是问题所在！")
                    
                    # 查看一些具体的记录
                    print("\n查看具体记录:")
                    for i in range(min(5, len(df))):
                        row = df.iloc[i]
                        print(f"Job {row['job_file_id']}: bk_count={row['bk_count']}, bl_count={row['bl_count']}")
                else:
                    print("✅ 找到了有booking数据的Job")
            else:
                print("❌ 结果中没有bk_count或bl_count字段")
                
        else:
            print("❌ 没有返回结果或结果为空")
            
    except Exception as e:
        print(f"❌ 函数调用失败: {e}")
        logger.error(f"Function call failed: {e}", exc_info=True)
        return False
    
    return True

def main():
    """
    主函数
    """
    try:
        print("开始测试原始统计函数")
        
        success = test_original_function()
        
        if success:
            print("\n✅ 测试完成")
        else:
            print("\n❌ 测试失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        logger.error(f"Program failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
