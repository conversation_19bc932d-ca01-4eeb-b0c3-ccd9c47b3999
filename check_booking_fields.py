#!/usr/bin/env python3
"""
检查booking数据的实际字段
"""

import sys
import os
import asyncio

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database.db_pro2_basic import get_sea_air_profit_with_transhipment
from utils.basic.optimized_export import BOOKING_COLUMN_MAPPING, BOOKING_EXPORT_ORDER

async def check_booking_fields():
    """检查booking数据的实际字段"""
    begin_date = "2025-05-01"
    end_date = "2025-05-31"
    
    print("正在获取booking数据...")
    booking_result = await get_sea_air_profit_with_transhipment(begin_date, end_date)
    
    if isinstance(booking_result, dict):
        booking_data = booking_result.get('data', [])
    else:
        booking_data = booking_result if booking_result else []
    
    if not booking_data:
        print("没有获取到booking数据")
        return
    
    # 获取第一条记录的所有字段
    sample_record = booking_data[0]
    actual_fields = set(sample_record.keys())
    
    print(f"\n=== 实际数据字段 ({len(actual_fields)}个) ===")
    for field in sorted(actual_fields):
        print(f"  {field}")
    
    # 检查配置中的字段
    config_fields = set(BOOKING_COLUMN_MAPPING.keys())
    print(f"\n=== 配置中的字段 ({len(config_fields)}个) ===")
    for field in BOOKING_EXPORT_ORDER:
        chinese_name = BOOKING_COLUMN_MAPPING.get(field, field)
        exists = "✓" if field in actual_fields else "✗"
        print(f"  {exists} {field} -> {chinese_name}")
    
    # 找出缺失的字段
    missing_fields = config_fields - actual_fields
    if missing_fields:
        print(f"\n=== 缺失的字段 ({len(missing_fields)}个) ===")
        for field in missing_fields:
            chinese_name = BOOKING_COLUMN_MAPPING.get(field, field)
            print(f"  ✗ {field} -> {chinese_name}")
    
    # 找出多余的字段
    extra_fields = actual_fields - config_fields
    if extra_fields:
        print(f"\n=== 数据中有但配置中没有的字段 ({len(extra_fields)}个) ===")
        for field in sorted(extra_fields):
            print(f"  + {field}")
    
    # 检查特定字段
    print(f"\n=== 特定字段检查 ===")
    check_fields = ['vessel', 'voyage', 'sailing_pol', 'bkbl_pol', 'bkbl_pod', 'salesman']
    for field in check_fields:
        exists = "✓" if field in actual_fields else "✗"
        chinese_name = BOOKING_COLUMN_MAPPING.get(field, "未配置")
        print(f"  {exists} {field} -> {chinese_name}")
    
    # 显示一些样本数据
    print(f"\n=== 样本数据 ===")
    sample_fields = ['vessel', 'voyage', 'sailing_pol', 'bkbl_pol', 'bkbl_pod', 'salesman', 'salesman_name']
    for field in sample_fields:
        if field in sample_record:
            value = sample_record[field]
            print(f"  {field}: {value}")
        else:
            print(f"  {field}: [字段不存在]")

if __name__ == "__main__":
    asyncio.run(check_booking_fields())
