#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI CMS Simplified - 企业内容管理系统精简版本

只保留核心功能：
1. Job/Booking数据导出
2. 人员/公司/部门名称查询
3. 系统监控
"""

import os
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, field_validator
import uvicorn

# 导入数据库操作函数
from utils.database.db_pro2_basic import (
    search_company_by_part_name,
    search_user_by_part_name,
    search_department_by_part_name,
    get_sea_air_profit_with_transhipment,
    get_job_details_with_transhipment
)

from utils.basic.logger_config import setup_logger
from utils.basic.optimized_export import export_to_oss

# 配置日志
logger = setup_logger(
    name=__name__,
    level="info",
    log_to_console=True,
    log_to_file=True
)

# 全局调度器变量
_profit_scheduler = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan handler that manages profit data scheduler startup and shutdown."""
    global _profit_scheduler

    # Startup
    # 检查是否启用自动提取功能
    enable_auto_extract = os.getenv('ENABLE_AUTO_EXTRACT', 'False').lower() == 'true'

    if enable_auto_extract:
        try:
            # 导入调度器类（延迟导入避免循环依赖）
            from ..basic.profit_data_scheduler import ProfitDataScheduler

            _profit_scheduler = ProfitDataScheduler()
            logger.info("正在启动利润数据周期性调度器...")

            # 在后台启动调度器
            _profit_scheduler.current_task = asyncio.create_task(_profit_scheduler.start_scheduler())

            logger.info("✅ 利润数据调度器已启动（后台运行）")
            logger.info("📋 调度规则：")
            logger.info("   • 距今2年以上：每3个月检查一次")
            logger.info("   • 距今1-2年：每3个月检查一次")
            logger.info("   • 距今3个月-1年：每1个月检查一次")
            logger.info("   • 距今1-3个月：每天检查一次")
            logger.info("   • 3个月数据周期，自动增量更新")
            logger.info("   • 执行时间：每日20:00至次日8:00")

        except Exception as e:
            logger.error(f"❌ 启动利润数据调度器失败: {e}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
    else:
        logger.info("⏸️ 自动提取功能已禁用")
        logger.info("💡 往期job和booking业务数据自动提取功能未启用")
        logger.info("   如需启用，请使用 --enable-auto-extract 参数重启服务器")
    
    yield
    
    # Shutdown
    if _profit_scheduler:
        try:
            logger.info("正在关闭利润数据调度器...")
            _profit_scheduler.is_running = False
            if _profit_scheduler.current_task:
                _profit_scheduler.current_task.cancel()
                try:
                    await _profit_scheduler.current_task
                except asyncio.CancelledError:
                    pass
            logger.info("✅ 利润数据调度器已关闭")
        except Exception as e:
            logger.error(f"❌ 关闭利润数据调度器失败: {e}")

app = FastAPI(
    title="CMS Simplified",
    description="企业内容管理系统精简版本 - 专注数据导出",
    version="4.0.0",
    lifespan=lifespan
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型定义
class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str = Field(..., min_length=1, description="搜索关键词")
    search_type: str = Field(default="company", description="搜索类型")
    
    @field_validator('search_type')
    @classmethod
    def validate_search_type(cls, v):
        if v not in ['company', 'user', 'department', 'salesman']:
            raise ValueError('搜索类型必须为 company, user, department 或 salesman')
        return v

class DateRangeRequest(BaseModel):
    """日期范围请求模型"""
    begin_date: str = Field(..., description="开始日期 (YYYY-MM-DD)")
    end_date: str = Field(..., description="结束日期 (YYYY-MM-DD)")
    format: str = Field(default="excel", description="导出格式")
    
    @field_validator('begin_date', 'end_date')
    @classmethod
    def validate_date_format(cls, v):
        try:
            datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError('日期格式必须为 YYYY-MM-DD')
    
    @field_validator('format')
    @classmethod
    def validate_format(cls, v):
        if v.lower() not in ['excel', 'csv']:
            raise ValueError('导出格式必须为 excel 或 csv')
        return v.lower()


class SystemStatus(BaseModel):
    """系统状态模型"""
    status: str
    timestamp: datetime
    database_connection: bool
    api_version: str
    feature_count: int


# 健康检查
@app.get("/health", response_model=SystemStatus)
async def health_check():
    """System health check endpoint that verifies database connectivity, API status, and core service availability. Returns comprehensive system status including connection status and feature availability."""
    try:
        await search_company_by_part_name("__test_connection__")
        db_status = True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        db_status = False
    
    return SystemStatus(
        status="healthy" if db_status else "degraded",
        timestamp=datetime.now(),
        database_connection=db_status,
        api_version="4.0.0",
        feature_count=3  # 名称查询、数据导出、健康检查
    )

# 统一搜索
@app.post("/search/", response_model=Dict[str, Any])
async def unified_search(request: SearchRequest):
    """Unified search service for finding companies, users, departments, and salespeople by partial name matching. Supports fuzzy search across all entity types in the logistics management system."""
    logger.info(f"统一搜索请求: 类型={request.search_type}, 关键词={request.query}")
    
    try:
        start_time = datetime.now()
        
        if request.search_type == "company":
            results = await search_company_by_part_name(request.query)
        elif request.search_type == "user":
            results = await search_user_by_part_name(request.query)
        elif request.search_type == "salesman":
            # 业务员搜索映射到用户搜索
            results = await search_user_by_part_name(request.query)
        elif request.search_type == "department":
            results = await search_department_by_part_name(request.query)
        else:
            raise HTTPException(status_code=400, detail="不支持的搜索类型")
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return {
            "success": True,
            "search_type": request.search_type,
            "query": request.query,
            "data": results or [],
            "count": len(results) if results else 0,
            "execution_time_seconds": execution_time,
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"统一搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

# 导出Booking数据
@app.post("/export/bookings", response_model=Dict[str, Any])
async def export_bookings(request: DateRangeRequest):
    """Export shipping booking data with profit analysis including revenue, costs, and carrier details for specified date range. Returns downloadable Excel/CSV file with TEU calculations and financial metrics."""
    try:
        logger.info(f"开始导出Booking数据: {request.begin_date} 至 {request.end_date}")
        start_time = datetime.now()
        
        try:
            data = await asyncio.wait_for(
                get_sea_air_profit_with_transhipment(
                    request.begin_date, 
                    request.end_date
                ),
                timeout=600.0
            )
        except asyncio.TimeoutError:
            logger.error(f"导出Booking数据超时: {request.begin_date} 至 {request.end_date}")
            raise HTTPException(
                status_code=408,
                detail="导出Booking数据超时，请缩小日期范围或联系管理员"
            )
        
        filename_prefix = f"bookings_{request.begin_date}_{request.end_date}"
        download_url = await export_to_oss(
            data, 
            filename_prefix, 
            data_type="booking",
            file_format=request.format
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 获取记录数
        record_count = data.get('total_count', 0) if isinstance(data, dict) else len(data)
        
        return {
            "success": True,
            "message": "Booking数据导出成功",
            "download_url": download_url,
            "record_count": record_count,
            "date_range": f"{request.begin_date} 至 {request.end_date}",
            "format": request.format,
            "execution_time_seconds": execution_time,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"导出Booking数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出Booking数据失败: {str(e)}")

# 导出Job数据
@app.post("/export/jobs", response_model=Dict[str, Any])
async def export_jobs(request: DateRangeRequest):
    """Export job order data with detailed logistics operations including container tracking, consolidation details, approval workflows, and business metrics for specified date range. Returns downloadable Excel/CSV file."""
    try:
        logger.info(f"开始导出Job数据: {request.begin_date} 至 {request.end_date}")
        start_time = datetime.now()
        
        try:
            data = await asyncio.wait_for(
                get_job_details_with_transhipment(
                    request.begin_date, 
                    request.end_date
                ),
                timeout=600.0
            )
        except asyncio.TimeoutError:
            logger.error(f"导出Job数据超时: {request.begin_date} 至 {request.end_date}")
            raise HTTPException(
                status_code=408,
                detail="导出Job数据超时，请缩小日期范围或联系管理员"
            )
        
        filename_prefix = f"jobs_{request.begin_date}_{request.end_date}"
        download_url = await export_to_oss(
            data, 
            filename_prefix, 
            data_type="job",
            file_format=request.format
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 获取记录数并检查数据是否为空
        record_count = data.get('total_count', 0) if isinstance(data, dict) else len(data)
        actual_data = data.get('data', []) if isinstance(data, dict) else data
        
        if not actual_data or len(actual_data) == 0:
            logger.warning(f"Job数据导出结果为空: {request.begin_date} 至 {request.end_date}")
            return {
                "success": False,
                "message": "指定日期范围内没有找到Job数据，请检查日期范围或数据库连接",
                "download_url": None,
                "record_count": 0,
                "date_range": f"{request.begin_date} 至 {request.end_date}",
                "format": request.format,
                "execution_time_seconds": execution_time,
                "timestamp": datetime.now().isoformat(),
                "suggestion": "请尝试扩大日期范围或检查数据库连接配置"
            }
        
        return {
            "success": True,
            "message": "Job数据导出成功",
            "download_url": download_url,
            "record_count": record_count,
            "date_range": f"{request.begin_date} 至 {request.end_date}",
            "format": request.format,
            "execution_time_seconds": execution_time,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"导出Job数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出Job数据失败: {str(e)}")


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
    