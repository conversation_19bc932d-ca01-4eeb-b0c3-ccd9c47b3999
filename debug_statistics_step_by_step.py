#!/usr/bin/env python3
"""
逐步调试统计函数
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.basic.db_pro2_sea_air_profit import (
    query_job_details_by_date,
    query_business_details_by_date
)

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_statistics_step_by_step():
    """
    逐步调试统计函数
    """
    print("=== 逐步调试统计函数 ===")
    
    # 设置日期范围
    begin_date = "2025-05-01"
    end_date = "2025-05-31"
    logger_prefix = "[DEBUG_STEP]"
    
    print(f"查询日期范围: {begin_date} to {end_date}")
    
    # 设置环境变量
    os.environ['TRANSHIPMENT_NEED'] = 'False'
    
    # 步骤1：检查环境变量
    print(f"\n--- 步骤1：检查环境变量 ---")
    transhipment_need = os.getenv('TRANSHIPMENT_NEED', 'False').lower() == 'true'
    print(f"TRANSHIPMENT_NEED: {os.getenv('TRANSHIPMENT_NEED')}")
    print(f"transhipment_need: {transhipment_need}")
    
    # 步骤2：计算扩展日期范围
    print(f"\n--- 步骤2：计算扩展日期范围 ---")
    if transhipment_need:
        begin_dt = datetime.strptime(begin_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        extended_begin = (begin_dt - timedelta(days=30)).strftime('%Y-%m-%d')
        extended_end = (end_dt + timedelta(days=30)).strftime('%Y-%m-%d')
        print(f"扩展日期范围: {extended_begin} to {extended_end}")
    else:
        extended_begin = begin_date
        extended_end = end_date
        print(f"原始日期范围: {extended_begin} to {extended_end}")
    
    # 步骤3：查询Job数据
    print(f"\n--- 步骤3：查询Job数据 ---")
    job_details = query_job_details_by_date(
        begin_date=begin_date,
        end_date=end_date,
        include_profit=True,
        logger_prefix=f"{logger_prefix}[JOB]"
    )
    
    if not job_details:
        print("❌ 没有Job数据")
        return False
    
    print(f"✅ Job数据: {len(job_details)} 条")
    
    # 转换为DataFrame
    job_df = pd.DataFrame(job_details)
    job_file_ids = job_df['job_file_id'].tolist()
    print(f"Job file IDs: {len(job_file_ids)} 个")
    print(f"Job file IDs示例: {job_file_ids[:5]}")
    
    # 步骤4：查询Booking数据
    print(f"\n--- 步骤4：查询Booking数据 ---")
    booking_details = query_business_details_by_date(
        begin_date=extended_begin,
        end_date=extended_end,
        business_type='all',
        include_business_no=True,
        include_profit=True,
        logger_prefix=f"{logger_prefix}[BOOKING]"
    )
    
    print(f"Booking查询参数:")
    print(f"  begin_date: {extended_begin}")
    print(f"  end_date: {extended_end}")
    print(f"  business_type: all")
    print(f"  include_business_no: True")
    print(f"  include_profit: True")
    
    if not booking_details:
        print("❌ 没有Booking数据 - 这是问题所在！")
        return False
    
    print(f"✅ Booking数据: {len(booking_details)} 条")
    
    # 步骤5：转换为DataFrame并筛选
    print(f"\n--- 步骤5：转换为DataFrame并筛选 ---")
    booking_df = pd.DataFrame(booking_details)
    print(f"Booking DataFrame: {len(booking_df)} 行")
    
    # 筛选相关booking数据
    job_file_ids_set = set(job_file_ids)
    relevant_bookings = booking_df[booking_df['job_file_id'].isin(job_file_ids_set)]
    
    print(f"相关Booking数据: {len(relevant_bookings)} 条")
    
    if len(relevant_bookings) == 0:
        print("❌ 没有相关的Booking数据 - 这是问题所在！")
        
        # 检查job_file_id的匹配情况
        booking_job_ids = set(booking_df['job_file_id'].unique())
        job_job_ids = set(job_file_ids)
        
        print(f"Booking中的Job IDs数量: {len(booking_job_ids)}")
        print(f"Job中的Job IDs数量: {len(job_job_ids)}")
        print(f"交集数量: {len(booking_job_ids & job_job_ids)}")
        
        if len(booking_job_ids & job_job_ids) == 0:
            print("❌ 完全没有交集")
            print(f"Booking Job IDs示例: {list(booking_job_ids)[:5]}")
            print(f"Job Job IDs示例: {list(job_job_ids)[:5]}")
        else:
            print(f"✅ 有交集: {len(booking_job_ids & job_job_ids)} 个")
        
        return False
    
    # 步骤6：使用groupby分组
    print(f"\n--- 步骤6：使用groupby分组 ---")
    booking_groups = relevant_bookings.groupby('job_file_id')
    print(f"Booking groups: {len(booking_groups)} 个")
    print(f"Group keys示例: {list(booking_groups.groups.keys())[:5]}")
    
    # 步骤7：测试统计逻辑
    print(f"\n--- 步骤7：测试统计逻辑 ---")
    
    test_job_id = job_file_ids[0]
    print(f"测试Job ID: {test_job_id}")
    
    # 检查是否在groups中
    if test_job_id in booking_groups.groups:
        print("✅ Job ID在booking groups中")
        
        # 获取booking数据
        job_bookings = booking_groups.get_group(test_job_id)
        print(f"该Job的booking数据: {len(job_bookings)} 条")
        
        # 检查业务类型
        business_types = job_bookings['business_type'].unique()
        print(f"业务类型: {business_types}")
        
        # 计算统计
        if 'sea_export' in business_types:
            sea_export_bookings = job_bookings[job_bookings['business_type'] == 'sea_export']
            bk_count = len(sea_export_bookings)
            bl_count = len(sea_export_bookings)  # 简化版本
            print(f"海运出口统计: bk_count={bk_count}, bl_count={bl_count}")
        else:
            bk_count = 0
            bl_count = len(job_bookings)
            print(f"其他业务统计: bk_count={bk_count}, bl_count={bl_count}")
        
        if bk_count > 0 or bl_count > 0:
            print("✅ 统计结果正常")
        else:
            print("❌ 统计结果为0")
            
    else:
        print("❌ Job ID不在booking groups中 - 这是问题所在！")
        
        # 检查数据类型
        print(f"Job ID类型: {type(test_job_id)}")
        print(f"Group keys类型: {[type(k) for k in list(booking_groups.groups.keys())[:3]]}")
    
    # 步骤8：批量测试前10个Job
    print(f"\n--- 步骤8：批量测试前10个Job ---")
    
    success_count = 0
    fail_count = 0
    
    for i, job_file_id in enumerate(job_file_ids[:10]):
        if job_file_id in booking_groups.groups:
            success_count += 1
        else:
            fail_count += 1
            print(f"Job {job_file_id} 不在booking groups中")
    
    print(f"成功: {success_count}, 失败: {fail_count}")
    
    if fail_count > 0:
        print("❌ 有Job无法找到对应的booking数据")
    else:
        print("✅ 所有测试Job都能找到对应的booking数据")
    
    return True

def main():
    """
    主函数
    """
    try:
        print("开始逐步调试统计函数")
        
        success = debug_statistics_step_by_step()
        
        if success:
            print("\n✅ 调试完成")
        else:
            print("\n❌ 调试失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        logger.error(f"Program failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
