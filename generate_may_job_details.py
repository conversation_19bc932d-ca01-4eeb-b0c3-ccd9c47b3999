#!/usr/bin/env python3
"""
生成2025年5月1日至31日的完整Job明细Excel，包括转运利润
"""

import sys
import os
import pandas as pd
from datetime import datetime
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.basic.db_pro2_sea_air_profit import query_job_details_with_statistics_by_date

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def generate_may_job_details_excel():
    """
    生成2025年5月的Job明细Excel
    """
    print("=== 生成2025年5月Job明细Excel ===")
    
    # 设置日期范围
    begin_date = "2025-05-01"
    end_date = "2025-05-31"
    
    print(f"查询日期范围: {begin_date} to {end_date}")
    
    # 设置转运需求为True以获取完整的转运数据
    os.environ['TRANSHIPMENT_NEED'] = 'True'
    print("设置 TRANSHIPMENT_NEED=True 以获取完整转运数据")
    
    try:
        # 查询Job明细数据
        print("正在查询Job明细数据...")
        start_time = datetime.now()
        
        job_details = query_job_details_with_statistics_by_date(
            begin_date=begin_date,
            end_date=end_date,
            logger_prefix="[MAY_EXPORT]"
        )
        
        end_time = datetime.now()
        query_duration = (end_time - start_time).total_seconds()
        
        print(f"查询完成，耗时: {query_duration:.2f} 秒")
        print(f"获取到 {len(job_details)} 条Job记录")
        
        if not job_details:
            print("❌ 没有找到任何Job数据")
            return False
        
        # 转换为DataFrame
        df = pd.DataFrame(job_details)
        
        # 数据统计分析
        print("\n=== 数据统计分析 ===")
        print(f"总Job数: {len(df)}")
        
        # 检查booking数据
        jobs_with_bookings = df[(df['bk_count'] > 0) | (df['bl_count'] > 0)]
        print(f"有booking数据的Job数: {len(jobs_with_bookings)}")
        
        if len(jobs_with_bookings) > 0:
            print(f"  - 平均bk_count: {jobs_with_bookings['bk_count'].mean():.2f}")
            print(f"  - 平均bl_count: {jobs_with_bookings['bl_count'].mean():.2f}")
            print(f"  - 总RT: {jobs_with_bookings['total_rt'].sum():.2f}")
            print(f"  - 总TEU: {jobs_with_bookings['total_teu'].sum():.2f}")
            print(f"  - 总空运重量: {jobs_with_bookings['total_air_weight'].sum():.2f}")
        
        # 检查转运数据
        jobs_with_transhipment = df[df['transhipment_count'] > 0]
        print(f"有转运数据的Job数: {len(jobs_with_transhipment)}")
        
        if len(jobs_with_transhipment) > 0:
            total_transhipment_profit = jobs_with_transhipment['transhipment_profit'].sum()
            print(f"  - 总转运利润: {total_transhipment_profit:.2f}")
            print(f"  - 平均转运数量: {jobs_with_transhipment['transhipment_count'].mean():.2f}")
        
        # 检查集拼数据
        jobs_with_consol = df[df['is_consol'] > 0]
        print(f"集拼Job数: {len(jobs_with_consol)}")
        
        if len(jobs_with_consol) > 0:
            print(f"  - 20'集拼容器总数: {jobs_with_consol['consol_20_count'].sum()}")
            print(f"  - 40'集拼容器总数: {jobs_with_consol['consol_40_count'].sum()}")
        
        # 检查指定货数据
        jobs_with_nominated = df[df['all_nominated_count'] > 0]
        print(f"有指定货的Job数: {len(jobs_with_nominated)}")
        
        if len(jobs_with_nominated) > 0:
            total_nominated_profit = jobs_with_nominated['all_nominated_profit'].sum()
            print(f"  - 总指定货利润: {total_nominated_profit:.2f}")
        
        # 按业务类型统计
        if 'business_type' in df.columns:
            print("\n=== 按业务类型统计 ===")
            business_type_stats = df.groupby('business_type').agg({
                'job_file_id': 'count',
                'income': 'sum',
                'cost': 'sum',
                'profit': 'sum',
                'total_rt': 'sum',
                'total_teu': 'sum',
                'total_air_weight': 'sum',
                'transhipment_profit': 'sum'
            }).round(2)
            
            for business_type, stats in business_type_stats.iterrows():
                print(f"{business_type}:")
                print(f"  - Job数: {stats['job_file_id']}")
                print(f"  - 收入: {stats['income']:.2f}")
                print(f"  - 成本: {stats['cost']:.2f}")
                print(f"  - 利润: {stats['profit']:.2f}")
                print(f"  - 转运利润: {stats['transhipment_profit']:.2f}")
        
        # 生成Excel文件
        print("\n=== 生成Excel文件 ===")
        
        # 重新排列列的顺序，把重要字段放在前面
        column_order = [
            'job_file_id', 'job_file_no', 'business_type', 'business_type_name',
            'income', 'cost', 'profit',
            'bk_count', 'bl_count', 'is_consol', 'consol_20_count', 'consol_40_count',
            'total_rt', 'total_teu', 'total_air_weight',
            'transhipment_count', 'transhipment_rt', 'transhipment_profit',
            'all_nominated_count', 'all_nominated_rt', 'all_nominated_profit',
            'port_agent_nominated_count', 'port_agent_nominated_rt', 'port_agent_nominated_profit'
        ]
        
        # 确保所有列都存在，如果不存在则添加默认值
        for col in column_order:
            if col not in df.columns:
                df[col] = 0
        
        # 添加其他可能存在的列
        existing_columns = [col for col in column_order if col in df.columns]
        other_columns = [col for col in df.columns if col not in column_order]
        final_columns = existing_columns + other_columns
        
        # 重新排序DataFrame
        df_export = df[final_columns]
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"job_details_2025_05_{timestamp}.xlsx"
        
        # 导出Excel
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 主数据表
            df_export.to_excel(writer, sheet_name='Job明细', index=False)
            
            # 统计汇总表
            if len(jobs_with_bookings) > 0:
                summary_data = {
                    '统计项目': [
                        '总Job数', '有booking数据的Job数', '转运Job数', '集拼Job数', '指定货Job数',
                        '总收入', '总成本', '总利润', '总转运利润', '总指定货利润',
                        '总RT', '总TEU', '总空运重量'
                    ],
                    '数值': [
                        len(df),
                        len(jobs_with_bookings),
                        len(jobs_with_transhipment),
                        len(jobs_with_consol),
                        len(jobs_with_nominated),
                        df['income'].sum(),
                        df['cost'].sum(),
                        df['profit'].sum(),
                        df['transhipment_profit'].sum(),
                        df['all_nominated_profit'].sum(),
                        df['total_rt'].sum(),
                        df['total_teu'].sum(),
                        df['total_air_weight'].sum()
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='统计汇总', index=False)
            
            # 业务类型汇总表
            if 'business_type' in df.columns and len(business_type_stats) > 0:
                business_type_stats.to_excel(writer, sheet_name='业务类型汇总')
        
        print(f"✅ Excel文件已生成: {filename}")
        print(f"包含以下工作表:")
        print(f"  - Job明细: {len(df_export)} 条记录")
        print(f"  - 统计汇总: 关键指标汇总")
        if 'business_type' in df.columns:
            print(f"  - 业务类型汇总: 按业务类型的统计数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成Excel失败: {e}")
        logger.error(f"Excel generation failed: {e}", exc_info=True)
        return False

def main():
    """
    主函数
    """
    try:
        print("开始生成2025年5月Job明细Excel")
        
        success = generate_may_job_details_excel()
        
        if success:
            print("\n✅ Excel生成完成")
        else:
            print("\n❌ Excel生成失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        logger.error(f"Program failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
