#!/usr/bin/env python3
"""
调试Job和Booking数据关联问题
"""

import sys
import os
import pandas as pd
from datetime import datetime
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.basic.db_pro2_sea_air_profit import (
    query_job_details_by_date,
    query_business_details_by_date
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_data_association():
    """
    调试数据关联问题
    """
    print("=== 调试Job和Booking数据关联问题 ===")
    
    # 设置日期范围
    begin_date = "2025-05-01"
    end_date = "2025-05-31"
    
    print(f"查询日期范围: {begin_date} to {end_date}")
    
    # 1. 查询Job数据
    print("\n--- 查询Job数据 ---")
    job_details = query_job_details_by_date(
        begin_date=begin_date,
        end_date=end_date,
        include_profit=True,
        logger_prefix="[DEBUG_JOB]"
    )
    
    print(f"Job数据: {len(job_details)} 条记录")
    
    if job_details:
        job_df = pd.DataFrame(job_details)
        print("Job数据字段:", list(job_df.columns))
        print("Job数据样例:")
        print(job_df[['job_file_id', 'business_type', 'income', 'cost', 'profit']].head())
        
        # 获取所有job_file_id
        job_file_ids = job_df['job_file_id'].unique()
        print(f"唯一Job ID数量: {len(job_file_ids)}")
        print(f"Job ID范围: {min(job_file_ids)} - {max(job_file_ids)}")
    
    # 2. 查询Booking数据
    print("\n--- 查询Booking数据 ---")
    os.environ['TRANSHIPMENT_NEED'] = 'False'
    
    booking_details = query_business_details_by_date(
        begin_date=begin_date,
        end_date=end_date,
        business_type='all',
        include_business_no=True,
        include_profit=True,
        logger_prefix="[DEBUG_BOOKING]"
    )
    
    print(f"Booking数据: {len(booking_details)} 条记录")
    
    if booking_details:
        booking_df = pd.DataFrame(booking_details)
        print("Booking数据字段:", list(booking_df.columns))
        print("Booking数据样例:")
        if 'job_file_id' in booking_df.columns:
            print(booking_df[['job_file_id', 'business_type', 'income', 'cost', 'profit']].head())
            
            # 获取所有booking的job_file_id
            booking_job_ids = booking_df['job_file_id'].unique()
            print(f"Booking中唯一Job ID数量: {len(booking_job_ids)}")
            print(f"Booking Job ID范围: {min(booking_job_ids)} - {max(booking_job_ids)}")
        else:
            print("❌ Booking数据中没有job_file_id字段！")
            print("可用字段:", list(booking_df.columns))
    
    # 3. 检查数据关联
    if job_details and booking_details and 'job_file_id' in booking_df.columns:
        print("\n--- 检查数据关联 ---")
        
        # 找出有booking数据的job
        jobs_with_bookings = set(job_file_ids) & set(booking_job_ids)
        jobs_without_bookings = set(job_file_ids) - set(booking_job_ids)
        bookings_without_jobs = set(booking_job_ids) - set(job_file_ids)
        
        print(f"有booking数据的Job数: {len(jobs_with_bookings)}")
        print(f"没有booking数据的Job数: {len(jobs_without_bookings)}")
        print(f"没有对应Job的Booking数: {len(bookings_without_jobs)}")
        
        if len(jobs_with_bookings) > 0:
            print(f"有booking数据的Job ID示例: {list(jobs_with_bookings)[:10]}")
        
        if len(jobs_without_bookings) > 0:
            print(f"没有booking数据的Job ID示例: {list(jobs_without_bookings)[:10]}")
            
            # 检查这些Job的业务类型
            jobs_without_bookings_df = job_df[job_df['job_file_id'].isin(jobs_without_bookings)]
            business_type_counts = jobs_without_bookings_df['business_type'].value_counts()
            print("没有booking数据的Job按业务类型分布:")
            for bt, count in business_type_counts.items():
                print(f"  {bt}: {count}")
        
        if len(bookings_without_jobs) > 0:
            print(f"没有对应Job的Booking ID示例: {list(bookings_without_jobs)[:10]}")
    
    # 4. 检查日期范围问题
    print("\n--- 检查日期范围问题 ---")
    
    if job_details:
        # 检查Job数据的日期字段
        date_fields = [col for col in job_df.columns if 'date' in col.lower()]
        print(f"Job数据中的日期字段: {date_fields}")
        
        for field in date_fields[:3]:  # 只检查前3个日期字段
            if field in job_df.columns:
                try:
                    job_df[field] = pd.to_datetime(job_df[field], errors='coerce')
                    date_range = job_df[field].dropna()
                    if len(date_range) > 0:
                        print(f"  {field}: {date_range.min()} 到 {date_range.max()}")
                except:
                    print(f"  {field}: 无法解析日期")
    
    if booking_details:
        # 检查Booking数据的日期字段
        date_fields = [col for col in booking_df.columns if 'date' in col.lower()]
        print(f"Booking数据中的日期字段: {date_fields}")
        
        for field in date_fields[:3]:  # 只检查前3个日期字段
            if field in booking_df.columns:
                try:
                    booking_df[field] = pd.to_datetime(booking_df[field], errors='coerce')
                    date_range = booking_df[field].dropna()
                    if len(date_range) > 0:
                        print(f"  {field}: {date_range.min()} 到 {date_range.max()}")
                except:
                    print(f"  {field}: 无法解析日期")
    
    # 5. 生成详细的关联分析报告
    if job_details and booking_details and 'job_file_id' in booking_df.columns:
        print("\n--- 生成关联分析报告 ---")
        
        # 按业务类型分析关联情况
        for business_type in job_df['business_type'].unique():
            type_jobs = job_df[job_df['business_type'] == business_type]['job_file_id'].unique()
            type_bookings = booking_df[booking_df['business_type'] == business_type]['job_file_id'].unique() if 'business_type' in booking_df.columns else []
            
            jobs_with_bookings_type = set(type_jobs) & set(type_bookings)
            
            print(f"{business_type}:")
            print(f"  Job数: {len(type_jobs)}")
            print(f"  有booking的Job数: {len(jobs_with_bookings_type)}")
            print(f"  关联率: {len(jobs_with_bookings_type)/len(type_jobs)*100:.1f}%" if len(type_jobs) > 0 else "  关联率: N/A")
    
    return True

def main():
    """
    主函数
    """
    try:
        print("开始调试数据关联问题")
        
        success = debug_data_association()
        
        if success:
            print("\n✅ 调试完成")
        else:
            print("\n❌ 调试失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        logger.error(f"Program failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
