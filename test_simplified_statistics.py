#!/usr/bin/env python3
"""
测试简化版本的统计函数
"""

import sys
import os
import pandas as pd
from datetime import datetime
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.basic.db_pro2_sea_air_profit import (
    query_job_details_by_date,
    query_business_details_by_date
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_simplified_statistics():
    """
    测试简化版本的统计函数
    """
    print("=== 测试简化版本的统计函数 ===")
    
    # 设置日期范围
    begin_date = "2025-05-01"
    end_date = "2025-05-31"
    
    print(f"查询日期范围: {begin_date} to {end_date}")
    
    # 1. 查询Job数据
    job_details = query_job_details_by_date(
        begin_date=begin_date,
        end_date=end_date,
        include_profit=True,
        logger_prefix="[TEST_JOB]"
    )
    
    # 2. 查询Booking数据
    os.environ['TRANSHIPMENT_NEED'] = 'False'
    
    booking_details = query_business_details_by_date(
        begin_date=begin_date,
        end_date=end_date,
        business_type='all',
        include_business_no=True,
        include_profit=True,
        logger_prefix="[TEST_BOOKING]"
    )
    
    if not job_details or not booking_details:
        print("❌ 没有数据")
        return False
    
    # 转换为DataFrame
    job_df = pd.DataFrame(job_details)
    booking_df = pd.DataFrame(booking_details)
    
    print(f"Job数据: {len(job_df)} 条")
    print(f"Booking数据: {len(booking_df)} 条")
    
    # 获取job_file_ids
    job_file_ids = job_df['job_file_id'].tolist()
    
    # 筛选相关booking数据
    job_file_ids_set = set(job_file_ids)
    relevant_bookings = booking_df[booking_df['job_file_id'].isin(job_file_ids_set)]
    
    print(f"相关Booking数据: {len(relevant_bookings)} 条")
    
    # 使用groupby分组
    if len(relevant_bookings) > 0:
        booking_groups = relevant_bookings.groupby('job_file_id')
    else:
        booking_groups = {}
    
    print(f"Booking groups数量: {len(booking_groups) if hasattr(booking_groups, '__len__') else 'N/A'}")
    
    # 模拟统计逻辑
    results = []
    jobs_with_bookings = 0
    jobs_without_bookings = 0
    
    for i, job_file_id in enumerate(job_file_ids[:10]):  # 只测试前10个
        print(f"\n--- 处理Job {i+1}: {job_file_id} ---")
        
        # 检查是否有booking数据
        if hasattr(booking_groups, 'groups') and job_file_id in booking_groups.groups:
            print(f"✅ Job {job_file_id} 有booking数据")
            jobs_with_bookings += 1
            
            # 获取booking数据
            job_bookings = booking_groups.get_group(job_file_id)
            print(f"  Booking记录数: {len(job_bookings)}")
            
            # 检查业务类型
            business_types = job_bookings['business_type'].unique() if 'business_type' in job_bookings.columns else []
            print(f"  业务类型: {business_types}")
            
            # 计算基础统计
            bk_count = 0
            bl_count = 0
            
            if 'sea_export' in business_types:
                sea_export_bookings = job_bookings[job_bookings['business_type'] == 'sea_export']
                bk_count = len(sea_export_bookings)
                bl_count = len(sea_export_bookings)  # 简化版本，不查询实际BL数量
                print(f"  海运出口 - bk_count: {bk_count}, bl_count: {bl_count}")
            else:
                bk_count = 0
                bl_count = len(job_bookings)
                print(f"  其他业务 - bk_count: {bk_count}, bl_count: {bl_count}")
            
            # 计算RT/TEU/空运重量
            total_rt = job_bookings['lcl_rt'].sum() if 'lcl_rt' in job_bookings.columns else 0
            total_teu = job_bookings['teu'].sum() if 'teu' in job_bookings.columns else 0
            total_air_weight = job_bookings['air_weight'].sum() if 'air_weight' in job_bookings.columns else 0
            
            print(f"  RT: {total_rt}, TEU: {total_teu}, 空运重量: {total_air_weight}")
            
            result = {
                'job_file_id': job_file_id,
                'bk_count': bk_count,
                'bl_count': bl_count,
                'total_rt': total_rt,
                'total_teu': total_teu,
                'total_air_weight': total_air_weight
            }
            
        else:
            print(f"❌ Job {job_file_id} 没有booking数据")
            jobs_without_bookings += 1
            
            result = {
                'job_file_id': job_file_id,
                'bk_count': 0,
                'bl_count': 0,
                'total_rt': 0,
                'total_teu': 0,
                'total_air_weight': 0
            }
        
        results.append(result)
    
    print(f"\n=== 统计结果 ===")
    print(f"测试的Job数: {len(results)}")
    print(f"有booking数据的Job数: {jobs_with_bookings}")
    print(f"没有booking数据的Job数: {jobs_without_bookings}")
    
    # 显示结果
    results_df = pd.DataFrame(results)
    print("\n结果样例:")
    print(results_df)
    
    # 检查是否所有统计都是0
    non_zero_bk = (results_df['bk_count'] > 0).sum()
    non_zero_bl = (results_df['bl_count'] > 0).sum()
    non_zero_rt = (results_df['total_rt'] > 0).sum()
    non_zero_teu = (results_df['total_teu'] > 0).sum()
    non_zero_air = (results_df['total_air_weight'] > 0).sum()
    
    print(f"\n非零统计:")
    print(f"  bk_count > 0: {non_zero_bk}")
    print(f"  bl_count > 0: {non_zero_bl}")
    print(f"  total_rt > 0: {non_zero_rt}")
    print(f"  total_teu > 0: {non_zero_teu}")
    print(f"  total_air_weight > 0: {non_zero_air}")
    
    if non_zero_bk > 0 or non_zero_bl > 0:
        print("✅ 找到了有booking数据的Job")
    else:
        print("❌ 所有Job的booking统计都是0")
    
    return True

def main():
    """
    主函数
    """
    try:
        print("开始测试简化版本的统计函数")
        
        success = test_simplified_statistics()
        
        if success:
            print("\n✅ 测试完成")
        else:
            print("\n❌ 测试失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        logger.error(f"Program failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
