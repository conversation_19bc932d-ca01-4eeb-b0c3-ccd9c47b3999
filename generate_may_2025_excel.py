#!/usr/bin/env python3
"""
生成2025年5月的实际job和booking数据Excel文件
"""

import sys
import os
import asyncio
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database.db_pro2_basic import get_job_details_with_transhipment, get_sea_air_profit_with_transhipment
from utils.basic.optimized_export import create_excel_file
from utils.basic.logger_config import setup_logger

# 配置日志
logger = setup_logger(
    name=__name__,
    level="info",
    log_to_console=True,
    log_to_file=True
)

async def generate_may_2025_excel():
    """生成2025年5月的Excel文件"""
    begin_date = "2025-05-01"
    end_date = "2025-05-31"
    
    logger.info(f"开始生成2025年5月数据Excel文件: {begin_date} 至 {end_date}")
    
    try:
        # 获取Job数据
        logger.info("正在获取Job数据...")
        job_result = await get_job_details_with_transhipment(begin_date, end_date)
        
        if isinstance(job_result, dict):
            job_data = job_result.get('data', [])
            job_total = job_result.get('total_count', 0)
        else:
            job_data = job_result if job_result else []
            job_total = len(job_data)
        
        logger.info(f"获取到Job数据: {job_total} 条记录")
        
        # 获取Booking数据
        logger.info("正在获取Booking数据...")
        booking_result = await get_sea_air_profit_with_transhipment(begin_date, end_date)
        
        if isinstance(booking_result, dict):
            booking_data = booking_result.get('data', [])
            booking_total = booking_result.get('total_count', 0)
        else:
            booking_data = booking_result if booking_result else []
            booking_total = len(booking_data)
        
        logger.info(f"获取到Booking数据: {booking_total} 条记录")
        
        # 生成Job Excel文件
        if job_data:
            logger.info("正在生成Job Excel文件...")
            job_title = f"2025年5月Job数据_{job_total}条记录"
            job_excel = create_excel_file(job_data, job_title, "job")
            
            job_filename = f"2025年5月Job数据_{job_total}条_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            with open(job_filename, "wb") as f:
                f.write(job_excel.getvalue())
            logger.info(f"Job Excel文件已生成: {job_filename}")
        else:
            logger.warning("没有Job数据，跳过生成Job Excel文件")
        
        # 生成Booking Excel文件
        if booking_data:
            logger.info("正在生成Booking Excel文件...")
            booking_title = f"2025年5月Booking数据_{booking_total}条记录"
            booking_excel = create_excel_file(booking_data, booking_title, "booking")
            
            booking_filename = f"2025年5月Booking数据_{booking_total}条_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            with open(booking_filename, "wb") as f:
                f.write(booking_excel.getvalue())
            logger.info(f"Booking Excel文件已生成: {booking_filename}")
        else:
            logger.warning("没有Booking数据，跳过生成Booking Excel文件")
        
        # 打印数据概览
        if job_data:
            logger.info("\n=== Job数据概览 ===")
            logger.info(f"总记录数: {job_total}")
            if job_data:
                sample_job = job_data[0]
                logger.info(f"字段数量: {len(sample_job)}")
                logger.info(f"主要字段: {list(sample_job.keys())[:10]}...")
        
        if booking_data:
            logger.info("\n=== Booking数据概览 ===")
            logger.info(f"总记录数: {booking_total}")
            if booking_data:
                sample_booking = booking_data[0]
                logger.info(f"字段数量: {len(sample_booking)}")
                logger.info(f"主要字段: {list(sample_booking.keys())[:10]}...")
        
        logger.info("Excel文件生成完成！")
        
        return {
            "job_count": job_total,
            "booking_count": booking_total,
            "job_filename": job_filename if job_data else None,
            "booking_filename": booking_filename if booking_data else None
        }
        
    except Exception as e:
        logger.error(f"生成Excel文件失败: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise

async def main():
    """主函数"""
    try:
        result = await generate_may_2025_excel()
        
        print("\n" + "="*50)
        print("2025年5月数据Excel文件生成结果")
        print("="*50)
        print(f"Job数据记录数: {result['job_count']}")
        print(f"Booking数据记录数: {result['booking_count']}")
        
        if result['job_filename']:
            print(f"Job Excel文件: {result['job_filename']}")
        
        if result['booking_filename']:
            print(f"Booking Excel文件: {result['booking_filename']}")
        
        print("="*50)
        
    except Exception as e:
        print(f"生成失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
