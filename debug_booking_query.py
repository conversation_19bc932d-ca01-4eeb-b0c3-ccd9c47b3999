#!/usr/bin/env python3
"""
调试booking查询问题
"""

import sys
import os
import pandas as pd
from datetime import datetime
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.basic.db_pro2_sea_air_profit import query_business_details_by_date

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_booking_query():
    """
    调试booking查询问题
    """
    print("=== 调试booking查询问题 ===")
    
    # 设置日期范围
    begin_date = "2025-05-01"
    end_date = "2025-05-31"
    
    print(f"查询日期范围: {begin_date} to {end_date}")
    
    # 设置环境变量
    os.environ['TRANSHIPMENT_NEED'] = 'False'
    
    print(f"TRANSHIPMENT_NEED: {os.getenv('TRANSHIPMENT_NEED')}")
    
    # 测试不同的查询参数组合
    test_cases = [
        {
            'name': '完整参数查询',
            'params': {
                'begin_date': begin_date,
                'end_date': end_date,
                'business_type': 'all',
                'include_business_no': True,
                'include_profit': True,
                'logger_prefix': "[TEST_FULL]"
            }
        },
        {
            'name': '最小参数查询',
            'params': {
                'begin_date': begin_date,
                'end_date': end_date,
                'logger_prefix': "[TEST_MIN]"
            }
        },
        {
            'name': '无logger_prefix查询',
            'params': {
                'begin_date': begin_date,
                'end_date': end_date,
                'business_type': 'all',
                'include_business_no': True,
                'include_profit': True
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['name']} ---")
        print(f"参数: {test_case['params']}")
        
        try:
            result = query_business_details_by_date(**test_case['params'])
            
            if result:
                print(f"✅ 查询成功，返回 {len(result)} 条记录")
                
                # 转换为DataFrame分析
                df = pd.DataFrame(result)
                print(f"字段: {list(df.columns)}")
                
                if 'job_file_id' in df.columns:
                    unique_jobs = df['job_file_id'].nunique()
                    print(f"唯一Job数: {unique_jobs}")
                    print(f"Job ID范围: {df['job_file_id'].min()} - {df['job_file_id'].max()}")
                
                if 'business_type' in df.columns:
                    business_types = df['business_type'].value_counts()
                    print(f"业务类型分布: {dict(business_types)}")
                
                # 显示前几条记录
                print("前3条记录:")
                display_cols = ['job_file_id', 'business_type']
                if 'income' in df.columns:
                    display_cols.append('income')
                if 'cost' in df.columns:
                    display_cols.append('cost')
                if 'profit' in df.columns:
                    display_cols.append('profit')
                
                print(df[display_cols].head(3))
                
            else:
                print("❌ 查询返回空结果")
                
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            logger.error(f"Query failed for {test_case['name']}: {e}", exc_info=True)
    
    # 特别测试：检查函数内部的查询逻辑
    print(f"\n--- 特别测试：模拟统计函数内部的查询 ---")
    
    try:
        # 这是统计函数内部使用的查询参数
        internal_result = query_business_details_by_date(
            begin_date=begin_date,
            end_date=end_date,
            business_type='all',
            include_business_no=True,
            include_profit=True,
            logger_prefix="[TEST_INTERNAL]"
        )
        
        print(f"内部查询结果: {len(internal_result) if internal_result else 0} 条记录")
        
        if internal_result:
            print("✅ 内部查询成功")
            
            # 检查是否为空的判断
            if not internal_result:
                print("❌ 内部查询结果被判断为空（这是问题所在）")
            else:
                print("✅ 内部查询结果不为空")
                
        else:
            print("❌ 内部查询返回空结果（这是问题所在）")
            
            # 尝试不同的参数组合
            print("\n尝试其他参数组合:")
            
            # 尝试不指定business_type
            alt_result1 = query_business_details_by_date(
                begin_date=begin_date,
                end_date=end_date,
                logger_prefix="[TEST_ALT1]"
            )
            print(f"不指定business_type: {len(alt_result1) if alt_result1 else 0} 条记录")
            
            # 尝试不指定include参数
            alt_result2 = query_business_details_by_date(
                begin_date=begin_date,
                end_date=end_date,
                business_type='all',
                logger_prefix="[TEST_ALT2]"
            )
            print(f"不指定include参数: {len(alt_result2) if alt_result2 else 0} 条记录")
            
    except Exception as e:
        print(f"❌ 内部查询测试失败: {e}")
        logger.error(f"Internal query test failed: {e}", exc_info=True)
    
    return True

def main():
    """
    主函数
    """
    try:
        print("开始调试booking查询问题")
        
        success = debug_booking_query()
        
        if success:
            print("\n✅ 调试完成")
        else:
            print("\n❌ 调试失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        logger.error(f"Program failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
