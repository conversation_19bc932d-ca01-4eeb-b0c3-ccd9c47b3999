# get_booking_details_with_transhipment 函数实现文档

## 概述

`get_booking_details_with_transhipment` 函数是在 `query_business_details_by_date` 函数基础上的扩展，专门用于查询包含转运业务利润数据的Booking级别业务详情。

## 功能特点

### 1. 基础数据获取
- 基于 `query_business_details_by_date` 函数获取指定日期范围的booking详情数据
- 包含完整的业务信息：业务号码、利润数据、RT/TEU/空运重量等

### 2. 转运数据扩展
- 根据 `TRANSHIPMENT_NEED` 环境变量决定是否扩展查询日期范围：
  - 如果 `TRANSHIPMENT_NEED=True`：扩展前后各30天，确保获取完整的转运业务数据
  - 如果 `TRANSHIPMENT_NEED=False` 或未设置：使用原始日期范围查询
- 通过 `transhipment_id` 关联查找转运业务的另一段数据
- 计算转运业务对应的利润数据（不包含RT，因为转运业务的RT是相同的）

### 3. 数据完整性
- 对于非转运业务，`transhipment_profit` 字段设置为0.0
- 对于转运业务但找不到对应数据的情况，同样设置为0.0
- 确保所有返回记录都包含转运相关字段

## 函数签名

```python
def get_booking_details_with_transhipment(begin_date: str, end_date: str, logger_prefix: str = "") -> List[Dict]:
```

### 参数说明
- `begin_date`: 开始日期，格式为 'YYYY-MM-DD'
- `end_date`: 结束日期，格式为 'YYYY-MM-DD'
- `logger_prefix`: 日志前缀，用于区分不同调用的日志输出

### 返回值
返回 `List[Dict]`，每个字典包含以下字段：

#### 基础字段（继承自 query_business_details_by_date）
- `jb_id`: 业务ID（字符串格式）
- `entity_id`: 实体ID
- `business_type`: 业务类型（'sea_export', 'sea_import', 'sea_triangle', 'air'）
- `business_type_name`: 业务类型中文名称
- `job_file_id`: 工作档ID
- `job_file_no`: 工作档号
- `business_no`: 业务号码
- `income`: 收入
- `cost`: 成本
- `profit`: 利润
- `lcl_rt`: LCL RT值
- `teu`: TEU值
- `air_weight`: 空运重量
- `is_transhipment`: 是否转运业务（0/1）
- `transhipment_id`: 转运ID
- 其他业务详情字段...

#### 新增转运字段
- `transhipment_profit`: 转运业务对应的利润值（float）

## 实现逻辑

### 第一步：获取基础数据
```python
base_booking_details = query_business_details_by_date(
    begin_date=begin_date,
    end_date=end_date,
    business_type='all',
    include_business_no=True,
    include_profit=True,
    logger_prefix=f"{logger_prefix}[BASE]"
)
```

### 第二步：根据环境变量决定日期范围
```python
transhipment_need = os.getenv('TRANSHIPMENT_NEED', 'False').lower() == 'true'

if transhipment_need:
    # 扩展前后各30天
    begin_dt = datetime.strptime(begin_date, '%Y-%m-%d')
    end_dt = datetime.strptime(end_date, '%Y-%m-%d')
    extended_begin = (begin_dt - timedelta(days=30)).strftime('%Y-%m-%d')
    extended_end = (end_dt + timedelta(days=30)).strftime('%Y-%m-%d')
else:
    # 使用原始日期范围
    extended_begin = begin_date
    extended_end = end_date
```

### 第三步：获取扩展范围的所有数据
```python
all_booking_details = query_business_details_by_date(
    begin_date=extended_begin,
    end_date=extended_end,
    business_type='all',
    include_business_no=True,
    include_profit=True,
    logger_prefix=f"{logger_prefix}[EXTENDED]"
)
```

### 第四步：计算转运数据
对于每个基础booking记录：
1. 检查 `is_transhipment` 和 `transhipment_id` 字段
2. 如果是转运业务，在扩展数据中查找相同 `transhipment_id` 的其他记录
3. 汇总对应记录的利润数据（不包含RT，因为转运业务的RT是相同的）
4. 将结果添加到 `transhipment_profit` 字段

## 使用示例

```python
from utils.basic.db_pro2_sea_air_profit import get_booking_details_with_transhipment

# 查询最近7天的booking数据（包含转运信息）
results = get_booking_details_with_transhipment(
    begin_date='2024-01-01',
    end_date='2024-01-07',
    logger_prefix="[TRANSHIPMENT_TEST]"
)

# 处理结果
for booking in results:
    if booking['is_transhipment']:
        print(f"转运业务 {booking['jb_id']}:")
        print(f"  本段RT: {booking['lcl_rt']}")
        print(f"  本段利润: {booking['profit']}")
        print(f"  转运利润: {booking['transhipment_profit']}")
```

## 测试结果

根据测试结果显示：
- 函数能够正确处理140条booking记录
- 识别出24条转运记录
- 其中19条记录成功计算出转运利润数据
- 所有记录都包含完整的字段结构

## 注意事项

1. **环境变量控制**：通过 `TRANSHIPMENT_NEED` 环境变量控制是否扩展日期范围
   - `TRANSHIPMENT_NEED=True`：扩展前后各30天（可能增加查询时间）
   - `TRANSHIPMENT_NEED=False` 或未设置：使用原始日期范围
2. **数据一致性**：转运数据的计算依赖于 `transhipment_id` 的准确性
3. **RT字段说明**：转运业务的RT不重复计算，因为是同一票业务
4. **性能考虑**：对于大日期范围的查询，建议分批处理
5. **错误处理**：函数包含完整的异常处理，查询失败时返回空列表

## 相关函数

- `query_business_details_by_date`: 基础booking数据查询函数
- `query_job_details_with_statistics_by_date`: Job级别的转运统计参考实现
- `calculate_profit_from_charges_unified`: 统一的利润计算函数
