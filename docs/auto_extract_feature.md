# 自动提取功能说明

## 概述

CMS企业管理系统现在支持通过命令行参数控制是否启用往期job和booking业务数据的自动提取功能。

## 功能特性

### 默认行为
- **默认状态**: 自动提取功能**默认关闭**
- **资源友好**: 不启用时不会消耗额外的系统资源
- **按需启用**: 只有在明确需要时才启用自动提取功能

### 自动提取功能详情
当启用自动提取功能时，系统将：

1. **数据范围**: 自动分析从2020年1月开始的所有job和booking业务数据
2. **智能检测**: 使用智能变更检测，只保存有变化的数据
3. **分层调度**: 根据数据的时间距离采用不同的检查频率：
   - 距今2年以上：每3个月检查一次
   - 距今1-2年：每3个月检查一次  
   - 距今3个月-1年：每1个月检查一次
   - 距今1-3个月：每天检查一次
4. **执行时间**: 每日20:00至次日8:00
5. **增量更新**: 3个月数据周期，自动增量更新

## 使用方法

### 启动服务器（默认不启用自动提取）
```bash
python mcp_server_cms.py
```

### 启动服务器并启用自动提取功能
```bash
python mcp_server_cms.py --enable-auto-extract
```

### 查看帮助信息
```bash
python mcp_server_cms.py --help
```

## 日志信息

### 自动提取功能禁用时
```
⏸️ 自动提取功能已禁用
💡 如需启用往期job和booking业务数据自动提取功能，请使用 --enable-auto-extract 参数
```

### 自动提取功能启用时
```
✅ 自动提取功能已启用 - 利润数据周期性调度器将在FastAPI应用启动时自动启动
📋 调度功能说明：
  - 自动分析job_details和booking_details数据
  - 智能变更检测，只保存有变化的数据
  - 分层调度：越久远的数据检查频率越低
  - 执行窗口：每日20:00至次日8:00
  - 数据范围：从2020年1月开始的所有业务数据
```

## 技术实现

### 参数传递机制
1. 命令行参数通过 `argparse` 解析
2. 参数值设置为环境变量 `ENABLE_AUTO_EXTRACT`
3. FastAPI应用在启动时检查环境变量决定是否启动调度器

### 代码修改点
1. `mcp_server_cms.py`: 添加命令行参数解析和环境变量设置
2. `utils/fastapi_apps/fastapi_cms_simplified.py`: 修改lifespan函数，根据参数控制调度器启动

## 注意事项

1. **资源消耗**: 启用自动提取功能会增加数据库查询和系统资源消耗
2. **执行时间**: 自动提取只在指定时间窗口内执行（每日20:00至次日8:00）
3. **数据完整性**: 自动提取功能会确保数据的完整性和一致性
4. **错误处理**: 包含完善的错误处理和重试机制

## 常见问题

### Q: 如何知道自动提取功能是否正在运行？
A: 查看服务器启动日志，会明确显示自动提取功能的状态。

### Q: 可以在运行时动态启用/禁用自动提取功能吗？
A: 目前需要重启服务器并使用相应的命令行参数。

### Q: 自动提取功能会影响正常的数据导出功能吗？
A: 不会。自动提取功能在后台运行，不会影响正常的API调用和数据导出功能。

### Q: 如何监控自动提取功能的运行状态？
A: 可以通过日志文件监控调度器的运行状态和数据处理情况。
